import { getActiveConcessions, hasOneRentBenefit, parseBenefits } from "./get-active-concessions.js";
import { fetchPropertyMetrics } from "./rent-by-units.js";
import { getAvgSimilars } from "./get-avg-similars.js";

async function analysePropertyConcessions(propertyIds) {
  return await analysePropertyConcessionsBatches(propertyIds);
}

async function analysePropertyConcessionsBatches(
  propertyIds,
  batchSize = 5,
  delayMs = 100,
  enableLogging = false
) {
  // Input validation
  if (!Array.isArray(propertyIds) || propertyIds.length === 0) {
    throw new Error('propertyIds must be a non-empty array');
  }

  const results = [];
  const totalBatches = Math.ceil(propertyIds.length / batchSize);

  for (let i = 0; i < propertyIds.length; i += batchSize) {
    const batch = propertyIds.slice(i, i + batchSize);
    const currentBatch = Math.floor(i / batchSize) + 1;

    if (enableLogging) {
      console.log(`Processing batch ${currentBatch} of ${totalBatches} (${batch.length} properties)`);
    }

    // Process batch
    const batchResults = await Promise.allSettled(
      batch.map(propertyId => analyzeProperty(propertyId))
    );

    // Process batch results
    const processedResults = batchResults.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          propertyId: batch[index],
          status: 'ERROR',
          error: result.reason?.message || 'Unknown error'
        };
      }
    });

    results.push(...processedResults);

    // Pause between batches (except for the last one)
    if (i + batchSize < propertyIds.length && delayMs > 0) {
      await sleep(delayMs);
    }
  }

  return results;
}

async function analyzeProperty(propertyId) {
  try {
    // Validate property ID
    if (!propertyId) {
      throw new Error('propertyId cannot be empty');
    }

    const concessions = await getActiveConcessions(propertyId);

    // No concessions
    if (!concessions?.length) {
      return {
        propertyId,
        status: 'NO_CONCESSIONS',
        concession_text: '',
        benefits: [],
        rent: {}
      };
    }

    // With concessions but no rent benefits
    if (!concessions.some(hasOneRentBenefit)) {
      return {
        propertyId,
        status: 'WITH_CONCESSIONS_NO_BENEFITS',
        concession_text: '',
        benefits: [],
        rent: {}

      };
    }

    // With rent benefits - needs additional analysis
    const { _response } = await fetchPropertyMetrics(propertyId);
    if (!Array.isArray(_response) || _response.length === 0) {
      return { propertyId, status: "NO_RENT_FOUND", details: "No rent found" };
    }
    const similarRents = getAvgSimilars(_response);

    // Process concessions with rent benefits
    const concessionsWithBenefits = concessions.filter(concession => hasOneRentBenefit(concession));

    if (concessionsWithBenefits.length > 0) {
      const concession = concessionsWithBenefits[0]; // Take the first one

      if (!similarRents.match) {
        return {
          propertyId,
          status: 'OK',
          concession_text: concession.description,
          benefits: parseBenefits(concession.benefits),
          rent: similarRents.metrics
        };
      }

      return {
        propertyId,
        status: 'OJO_CON_ESTE',
        concession_text: concession.description,
        benefits: parseBenefits(concession.benefits),
        rent: similarRents.metrics
      };
    }

    // Fallback return if no concessions with benefits found
    return {
      propertyId,
      status: 'WITH_CONCESSIONS_NO_BENEFITS',
      concession_text: '',
      benefits: [],
      rent: {}
    };

  } catch (error) {
    throw new Error(`Error analyzing property ${propertyId}: ${error.message}`);
  }
}

/**
 * Helper function for delays
 * @param {number} ms - Milliseconds to wait
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Example usage:
// const results = await analysePropertyConcessionsBatches(
//   propertyIds,
//   5,     // batchSize
//   100,   // delayMs
//   true   // enableLogging
// );

// Helper function to get analysis statistics
function getAnalysisStats(results) {
  const stats = results.reduce((acc, result) => {
    acc[result.status] = (acc[result.status] || 0) + 1;
    return acc;
  }, {});

  return {
    total: results.length,
    breakdown: stats,
    errors: results.filter(r => r.status === 'ERROR')
  };
}

export { analysePropertyConcessions, getAnalysisStats };