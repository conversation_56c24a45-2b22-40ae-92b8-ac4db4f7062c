import { getActiveConcessions, hasOneRentBenefit, parseBenefits } from "./get-active-concessions.js";
import { fetchPropertyMetrics } from "./rent-by-units.js";
import { getAvgSimilars } from "./get-avg-similars.js";

async function analysePropertyConcessions(propertyIds) {
  return await analysePropertyConcessionsBatches(propertyIds);
}

async function analysePropertyConcessionsBatches(
  propertyIds,
  batchSize = 5,
  delayMs = 100,
  enableLogging = false
) {
  // Validación de entrada
  if (!Array.isArray(propertyIds) || propertyIds.length === 0) {
    throw new Error('propertyIds debe ser un array no vacío');
  }

  const results = [];
  const totalBatches = Math.ceil(propertyIds.length / batchSize);

  for (let i = 0; i < propertyIds.length; i += batchSize) {
    const batch = propertyIds.slice(i, i + batchSize);
    const currentBatch = Math.floor(i / batchSize) + 1;

    if (enableLogging) {
      console.log(`Procesando lote ${currentBatch} de ${totalBatches} (${batch.length} propiedades)`);
    }

    // Procesamiento del lote
    const batchResults = await Promise.allSettled(
      batch.map(propertyId => analyzeProperty(propertyId))
    );

    // Procesar resultados del lote
    const processedResults = batchResults.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          propertyId: batch[index],
          status: 'ERROR',
          error: result.reason?.message || 'Error desconocido'
        };
      }
    });

    results.push(...processedResults);

    // Pausa entre lotes (excepto en el último)
    if (i + batchSize < propertyIds.length && delayMs > 0) {
      await sleep(delayMs);
    }
  }

  return results;
}

async function analyzeProperty(propertyId) {
  try {
    // Validar ID de propiedad
    if (!propertyId) {
      throw new Error('propertyId no puede estar vacío');
    }

    const concessions = await getActiveConcessions(propertyId);

    // Sin concesiones
    if (!concessions?.length) {
      return {
        propertyId,
        status: 'NO_CONCESSIONS',
        concession_text: '',
        benefits: []
      };
    }

    // Con concesiones pero sin beneficios de alquiler
    if (!concessions.some(hasOneRentBenefit)) {
      return {
        propertyId,
        status: 'WITH_CONCESSIONS_NO_BENEFITS',
        concession_text: '',
        benefits: []
      };
    }

    // Con beneficios de alquiler - necesita análisis adicional
    const { _response } = await fetchPropertyMetrics(propertyId);
    if (!Array.isArray(_response) || _response.length === 0) {
      return { propertyId, status: "NO_RENT_FOUND", details: "No se encontro renta" };
    }
    const similarRents = await getAvgSimilars(_response);

    concessions.filter(concession => hasOneRentBenefit(concession)).each(concession => {
      if (!similarRents?.length) {
        return {
          propertyId,
          status: 'OK',
          concession_text: concession.description,
          benefits: concessions.benefits
        };
      }

      return {
        propertyId,
        status: 'OJO_CON_ESTE',
        concession_text: concession.description,
        benefits: parseBenefits(concession.benefits)
      };
    });




  } catch (error) {
    throw new Error(`Error analizando propiedad ${propertyId}: ${error.message}`);
  }
}

/**
 * Función auxiliar para pausas
 * @param {number} ms - Milisegundos a esperar
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Ejemplo de uso:
// const results = await analysePropertyConcessionsBatches(
//   propertyIds, 
//   5,     // batchSize
//   100,   // delayMs
//   true   // enableLogging
// );

// Función auxiliar para obtener estadísticas de los resultados
function getAnalysisStats(results) {
  const stats = results.reduce((acc, result) => {
    acc[result.status] = (acc[result.status] || 0) + 1;
    return acc;
  }, {});

  return {
    total: results.length,
    breakdown: stats,
    errors: results.filter(r => r.status === 'ERROR')
  };
}

export { analysePropertyConcessions };