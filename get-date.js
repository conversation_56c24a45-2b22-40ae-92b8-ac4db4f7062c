function getCurrentDateFormatted() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
  
    return `${year}-${month}-${day}`;
  }

  function getTodayDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
  
    // console.log(`${year}-${month}-${day}`);
    return `${year}-${month}-${day}`;
  }

  function getDateIn7DaysLessThanToday() {
    const today = new Date();
    today.setDate(today.getDate() - 7);
  
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
  
    // console.log(`${year}-${month}-${day}`);
    return `${year}-${month}-${day}`;
  }

  export {
    getCurrentDateFormatted,
    getTodayDate,
    getDateIn7DaysLessThanToday,
  };