import { getDateIn7DaysLessThanToday, getTodayDate } from "./get-date.js";
import { getAuthToken } from "./get-access-token.js";

function hasOneRentBenefit(concession) {
    if (!concession?.benefits || !Array.isArray(concession.benefits)) {
        return false;
    }
    
    return concession.benefits.some(benefit => {
        return Object.entries(benefit).some(([key, value]) => {
            return value === true || (typeof value !== 'boolean' && value !== null && value !== undefined);
        });
    });
}


function parseBenefits(benefits) {
    return benefits.map(benefit => {
        return Object.entries(benefit).reduce((acc, [key, value]) => {
            if (value === true || (typeof value !== 'boolean' && value !== null && value !== undefined)) {
                acc.push(key);
            }
            return acc;
        }, []);
    });
}


async function getActiveConcessions(propertyId){
    const url = `https://rent-gw.whykeyway.com/v2/multifamily/concessions?dateFrom=${getDateIn7DaysLessThanToday()}&dateTo=${getTodayDate()}&propertyIds=${propertyId}`

    const response = await fetch(url, {
        method: 'GET',
        headers: {
            'accept': 'application/json',
            'Authorization': `Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
        }
    });
    
    if(!response.ok){
        throw new Error(`Error: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    // console.log(JSON.stringify(data, null, 2));
    const concessions = data.filter(data => data.active === true);
    // console.log(concessions);

    return concessions;
}

export { getActiveConcessions, hasOneRentBenefit, parseBenefits };