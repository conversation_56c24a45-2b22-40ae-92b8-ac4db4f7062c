async function getAuthToken() {
    const url = 'https://auth.dev.whykeyway.com/oauth/token';
  
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id: '7fqPwE1iEwYlMCx28QnlSeLjcLFiKf8c',
        client_secret: '8BxdpieTraWTcbDpV-m1VnE7vRds8abZweiRpOZsrzF9Vp-iu-Xcf4CJA0UL12Dv',
        realm: 'users-from-firebase',
        audience: 'https://keyway-api.dev.whykeyway.com',
        grant_type: 'http://auth0.com/oauth/grant-type/password-realm',
        username: '<EMAIL>',
        password: 'Luna1234'
      })
    });
  
    if (!response.ok) {
      throw new Error(`Error: ${response.status} - ${response.statusText}`);
    }
  
    const data = await response.json();
    return data.access_token;
  }

  export { getAuthToken };
  