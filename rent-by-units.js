import {getTodayDate, getDateIn7DaysLessThanToday} from './get-date.js';
import {getAuthToken} from './get-access-token.js';

async function fetchPropertyMetrics(propertyIds) {
    const url = `https://rent-api.whykeyway.com/multifamily/metrics/property?propertyIds=${propertyIds}&dateFrom=${getDateIn7DaysLessThanToday()}&dateTo=${getTodayDate()}`;
    console.log(url)
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      }
    });
  
    if (!response.ok) {
      throw new Error(`Error: ${response.status} - ${response.statusText}`);
    }
    
    const _response = await response.json();
    // console.log(JSON.stringify(_response, null, 2));
    return {_response};
  }

  export { fetchPropertyMetrics };
  

  