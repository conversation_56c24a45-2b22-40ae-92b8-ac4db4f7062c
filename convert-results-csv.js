import { promises as fs } from 'fs';
import path from 'path';


function convertResultsToCSV(results, options = {}) {
    const {
      separator = ',',
      includeDetails = true
    } = options;
  
    if (!Array.isArray(results) || results.length === 0) {
      throw new Error('Results debe ser un array no vacío');
    }
  
    let csvContent = '';
  
    // Headers de las columnas
    const headers = ['PropertyID', 'Status', 'ConcessionText', 'Benefits','Rent','Error'];
    
    csvContent += headers.join(separator) + '\n';
  
    // Datos
    results.forEach(result => {
      const row = [
        `"${result.propertyId}"`,
        `"${result.status}"`,
        `"${result.concession_text || ''}"`,
        `"${JSON.stringify(result.benefits) || ''}"`,
        `"${JSON.stringify(result.rent) || ''}"`,
        `"${result.error || ''}"`
      ];

      row.push();
      
      csvContent += row.join(separator) + '\n';
    });
  
    return csvContent;
  }

  async function saveResultsToCSV(results, filename = null, options = {}) {
    // Generar nombre de archivo si no se proporciona
    if (!filename) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
      filename = `property_analysis_${timestamp}.csv`;
    }
  
    // Asegurar extensión .csv
    if (!filename.endsWith('.csv')) {
      filename += '.csv';
    }
  
    try {
      const csvContent = convertResultsToCSV(results, options);
      await fs.writeFile(filename, csvContent, 'utf8');
      
      console.log(`✅ Archivo guardado exitosamente: ${path.resolve(filename)}`);
      return path.resolve(filename);
    } catch (error) {
      console.error('❌ Error guardando archivo CSV:', error.message);
      throw error;
    }
  }

  export { saveResultsToCSV };