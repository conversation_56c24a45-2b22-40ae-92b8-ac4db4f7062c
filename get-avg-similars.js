// async function validateMax(data) {
//   if (!Array.isArray(data) || data.length === 0) {
//     console.error("La respuesta no contiene propiedades.");
//     return;
//   }

//   data.forEach(property => {
//     const { propertyId, metrics } = property;

//     if (!Array.isArray(metrics) || metrics.length === 0) {
//       console.warn(`La property ${propertyId} no trae métricas.`);
//       return;
//     }

//     const matchingMetrics = metrics
//     .filter(m => m.askingRent?.average <= m.effectiveRent?.average)
//     .map(m => ({
//       avgAsking:    m.askingRent.average,
//       avgEffective: m.effectiveRent.average
//     }));

//     if (matchingMetrics.length === 0) {
//       // console.log(`Property ${propertyId}: no tiene unidades con valores de avg iguales.`);
//     } else {
//       console.log(`Property ${propertyId}: tiene ${matchingMetrics.length} métricas con avg iguales.`);
//       matchingMetrics.forEach(({ avgAsking, avgEffective }) => {
//         console.log(`El avgAsking es ${avgAsking} y el avgEffective es ${avgEffective}`);
        
//       });
//       // console.log(JSON.stringify(matchingMetrics, null, 2));
//     }
//   });
// }

async function getAvgSimilars(data) {
  data.forEach(property => {
    const { propertyId, metrics } = property;

    if (!Array.isArray(metrics) || metrics.length === 0) {
      console.warn(`La property ${propertyId} no trae métricas.`);
      return;
    }

    const matchingMetrics = metrics
    .filter(m => m.askingRent?.average <= m.effectiveRent?.average)
    .map(m => ({
      avgAsking:    m.askingRent.average,
      avgEffective: m.effectiveRent.average
    }));

  return matchingMetrics;
  });
}



export { getAvgSimilars };